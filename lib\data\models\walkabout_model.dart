import '../../domain/entities/walkabout.dart';

/// Data model for Walkabout entity
///
/// This model handles serialization/deserialization for different data sources
/// following Clean Architecture principles.
class WalkaboutModel extends Walkabout {
  const WalkaboutModel({
    required super.id,
    required super.title,
    super.description,
    required super.createdAt,
    required super.updatedAt,
    required super.status,
    super.location,
    required super.userId,
    required super.isCompleted,
    required super.syncStatus,
    super.deletedAt,
  });

  /// Create WalkaboutModel from domain entity
  factory WalkaboutModel.fromEntity(Walkabout walkabout) {
    return WalkaboutModel(
      id: walkabout.id,
      title: walkabout.title,
      description: walkabout.description,
      createdAt: walkabout.createdAt,
      updatedAt: walkabout.updatedAt,
      status: walkabout.status,
      location: walkabout.location,
      userId: walkabout.userId,
      isCompleted: walkabout.isCompleted,
      syncStatus: walkabout.syncStatus,
      deletedAt: walkabout.deletedAt,
    );
  }

  /// Create WalkaboutModel from SQLite map
  factory WalkaboutModel.fromMap(Map<String, dynamic> map) {
    return WalkaboutModel(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      status: _mapStringToWalkaboutStatus(map['status'] as String),
      location:
          map['location_lat'] != null && map['location_lng'] != null
              ? GeoPoint(
                latitude: map['location_lat'] as double,
                longitude: map['location_lng'] as double,
              )
              : null,
      userId: map['user_id'] as String,
      isCompleted: (map['is_completed'] as int) == 1,
      syncStatus: _mapStringToSyncStatus(map['sync_status'] as String),
      deletedAt:
          map['deleted_at'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['deleted_at'] as int)
              : null,
    );
  }

  /// Convert to SQLite map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'status': status.name,
      'location_lat': location?.latitude,
      'location_lng': location?.longitude,
      'user_id': userId,
      'is_completed': isCompleted ? 1 : 0,
      'sync_status': syncStatus.name,
      'deleted_at': deletedAt?.millisecondsSinceEpoch,
    };
  }

  /// Create WalkaboutModel from JSON (for Firestore)
  factory WalkaboutModel.fromJson(Map<String, dynamic> json) {
    return WalkaboutModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      status: _mapStringToWalkaboutStatus(json['status'] as String),
      location:
          json['location'] != null
              ? GeoPoint(
                latitude: json['location']['latitude'] as double,
                longitude: json['location']['longitude'] as double,
              )
              : null,
      userId: json['userId'] as String,
      isCompleted: json['isCompleted'] as bool,
      syncStatus:
          json['syncStatus'] != null
              ? _mapStringToSyncStatus(json['syncStatus'] as String)
              : SyncStatus.local,
      deletedAt:
          json['deletedAt'] != null
              ? DateTime.parse(json['deletedAt'] as String)
              : null,
    );
  }

  /// Convert to JSON (for Firestore)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'status': status.name,
      'location':
          location != null
              ? {
                'latitude': location!.latitude,
                'longitude': location!.longitude,
              }
              : null,
      'userId': userId,
      'isCompleted': isCompleted,
      'syncStatus': syncStatus.name,
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  /// Map string to WalkaboutStatus enum
  static WalkaboutStatus _mapStringToWalkaboutStatus(String status) {
    switch (status) {
      case 'draft':
        return WalkaboutStatus.draft;
      case 'inProgress':
        return WalkaboutStatus.inProgress;
      case 'completed':
        return WalkaboutStatus.completed;
      case 'archived':
        return WalkaboutStatus.archived;
      default:
        return WalkaboutStatus.draft;
    }
  }

  /// Map string to SyncStatus enum
  static SyncStatus _mapStringToSyncStatus(String status) {
    switch (status) {
      case 'local':
        return SyncStatus.local;
      case 'syncing':
        return SyncStatus.syncing;
      case 'synced':
        return SyncStatus.synced;
      case 'error':
        return SyncStatus.error;
      default:
        return SyncStatus.local;
    }
  }

  /// Create a copy with updated fields
  @override
  WalkaboutModel copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    WalkaboutStatus? status,
    GeoPoint? location,
    String? userId,
    bool? isCompleted,
    SyncStatus? syncStatus,
    DateTime? deletedAt,
  }) {
    return WalkaboutModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      location: location ?? this.location,
      userId: userId ?? this.userId,
      isCompleted: isCompleted ?? this.isCompleted,
      syncStatus: syncStatus ?? this.syncStatus,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }
}
