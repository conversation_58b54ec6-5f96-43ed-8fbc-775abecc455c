import '../../domain/entities/walkabout.dart';
import '../../domain/repositories/walkabout_repository.dart';
import '../datasources/local/walkabout_local_datasource.dart';

/// Implementation of WalkaboutRepository interface
///
/// This implementation currently uses only local data source for offline-first approach.
/// Remote synchronization will be added in Story 2.3.
class WalkaboutRepositoryImpl implements WalkaboutRepository {
  final WalkaboutLocalDataSource localDataSource;

  const WalkaboutRepositoryImpl({required this.localDataSource});

  @override
  Future<Walkabout> createWalkabout(Walkabout walkabout) async {
    try {
      return await localDataSource.createWalkabout(walkabout);
    } catch (e) {
      throw Exception('Failed to create walkabout: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout?> getWalkaboutById(String id) async {
    try {
      return await localDataSource.getWalkaboutById(id);
    } catch (e) {
      throw Exception('Failed to get walkabout by ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getWalkaboutsByUserId(String userId) async {
    try {
      return await localDataSource.getWalkaboutsByUserId(userId);
    } catch (e) {
      throw Exception('Failed to get walkabouts by user ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getWalkaboutsByStatus(
    String userId,
    WalkaboutStatus status,
  ) async {
    try {
      return await localDataSource.getWalkaboutsByStatus(userId, status);
    } catch (e) {
      throw Exception('Failed to get walkabouts by status: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout> updateWalkabout(Walkabout walkabout) async {
    try {
      // Update the updatedAt timestamp
      final updatedWalkabout = walkabout.copyWith(
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.local, // Mark as needing sync
      );

      return await localDataSource.updateWalkabout(updatedWalkabout);
    } catch (e) {
      throw Exception('Failed to update walkabout: ${e.toString()}');
    }
  }

  @override
  Future<bool> deleteWalkabout(String id) async {
    try {
      return await localDataSource.deleteWalkabout(id);
    } catch (e) {
      throw Exception('Failed to delete walkabout: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getWalkaboutsToSync() async {
    try {
      return await localDataSource.getWalkaboutsToSync();
    } catch (e) {
      throw Exception('Failed to get walkabouts to sync: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout> updateSyncStatus(String id, SyncStatus syncStatus) async {
    try {
      return await localDataSource.updateSyncStatus(id, syncStatus);
    } catch (e) {
      throw Exception('Failed to update sync status: ${e.toString()}');
    }
  }

  @override
  Future<bool> titleExistsForUser(String userId, String title) async {
    try {
      return await localDataSource.titleExistsForUser(userId, title);
    } catch (e) {
      throw Exception('Failed to check title existence: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout> restoreWalkabout(String id) async {
    try {
      return await localDataSource.restoreWalkabout(id);
    } catch (e) {
      throw Exception('Failed to restore walkabout: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getDeletedWalkaboutsByUserId(String userId) async {
    try {
      return await localDataSource.getDeletedWalkaboutsByUserId(userId);
    } catch (e) {
      throw Exception('Failed to get deleted walkabouts: ${e.toString()}');
    }
  }

  @override
  Future<bool> permanentlyDeleteWalkabout(String id) async {
    try {
      return await localDataSource.permanentlyDeleteWalkabout(id);
    } catch (e) {
      throw Exception(
        'Failed to permanently delete walkabout: ${e.toString()}',
      );
    }
  }
}
